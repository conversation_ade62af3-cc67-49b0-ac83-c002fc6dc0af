Weight,abs_rel,sq_rel,rmse,rmse_log,a1,a2,a3
weights_0,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_1,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_2,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_3,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_4,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_5,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_6,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_7,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_8,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_9,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_10,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_11,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_12,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_13,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_14,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_15,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_16,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_17,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_18,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_19,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

