#!/bin/bash

device=0
weights_dir="/mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models"
data_path="/mnt/acer/kitti_jpg"
temp_output="$weights_dir/eval_results_adaptive.csv"

# 初始化CSV文件，添加表头
echo "Weight,abs_rel,sq_rel,rmse,rmse_log,a1,a2,a3" > $temp_output

for i in $(seq 0 20)  # 临时只测试前两个权重
do
    name=${weights_dir}/weights_$i

    if [ ! -d "$name" ]; then
        echo "$name 不存在，跳过"
        continue
    fi

    output=$(CUDA_VISIBLE_DEVICES=$device \
        source ~/anaconda3/etc/profile.d/conda.sh && conda activate rtmonodepth && \
        python evaluate_flow.py \
              --load_weights_folder $name \
              --model_type adaptive \
              --eval_stereo \
              --data_path $data_path \
              --eval_split eigen \
              --num_workers 4 \
              --batch_size 8)

    abs_rel=$(echo "$output" | grep '&' | awk '{print $2}')
    sq_rel=$(echo "$output" | grep '&' | awk '{print $4}')
    rmse=$(echo "$output" | grep '&' | awk '{print $6}')
    rmse_log=$(echo "$output" | grep '&' | awk '{print $8}')
    a1=$(echo "$output" | grep '&' | awk '{print $10}')
    a2=$(echo "$output" | grep '&' | awk '{print $12}')
    a3=$(echo "$output" | grep '&' | awk '{print $14}')

    # 将每个权重的评估结果附加到临时文件中
    echo "weights_$i,$abs_rel,$sq_rel,$rmse,$rmse_log,$a1,$a2,$a3" >> $temp_output
    echo "$output" >> $temp_output
    echo "" >> $temp_output  # 添加空行以分隔每个权重的结果
done