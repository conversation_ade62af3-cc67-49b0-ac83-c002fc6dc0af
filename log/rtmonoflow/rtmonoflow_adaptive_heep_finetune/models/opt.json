{"data_path": "/mnt/acer/kitti_jpg", "log_dir": "./log/rtmonoflow", "model_name": "rt<PERSON><PERSON><PERSON>_adaptive_heep_finetune", "split": "eigen_zhou", "num_layers": 18, "freeze_encoder": false, "dataset": "kitti", "png": false, "height": 192, "width": 640, "disparity_smoothness": 0.001, "scales": [0, 1, 2], "min_depth": 0.1, "max_depth": 100.0, "use_stereo": true, "frame_ids": [0, -1, 1, "s"], "use_depth_hints": false, "depth_hint_path": null, "batch_size": 8, "learning_rate": 5e-05, "num_epochs": 10, "scheduler_step_size": 15, "v1_multiscale": false, "avg_reprojection": false, "disable_automasking": false, "predictive_mask": false, "no_ssim": false, "weights_init": "pretrained", "pose_model_input": "pairs", "pose_model_type": "separate_resnet", "use_rtmonoflow": true, "use_adaptive_heep": true, "use_flow_blocks": true, "heep_pyramid_levels": 4, "use_learnable_K": false, "use_feature_reprojection_loss": false, "no_cuda": false, "num_workers": 12, "load_weights_folder": "./log/rtmonoflow/rtmonoflow_adaptive_heep/models/weights_19", "models_to_load": ["encoder", "depth", "pose_encoder", "pose"], "log_frequency": 100, "save_frequency": 1, "eval_stereo": false, "eval_mono": false, "disable_median_scaling": false, "pred_depth_scale_factor": 1, "ext_disp_to_eval": null, "eval_split": "eigen", "save_pred_disps": false, "no_eval": false, "eval_eigen_to_benchmark": false, "eval_out_dir": null, "post_process": false, "use_booster": true, "use_acm": true, "use_s2conv": true}