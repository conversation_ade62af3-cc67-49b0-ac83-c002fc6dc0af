Weight,abs_rel,sq_rel,rmse,rmse_log,a1,a2,a3
weights_0,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_1,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_2,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_3,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

weights_4,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: RTMonoFlow evaluation failed: Could not find compatible model weights in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Falling back to baseline model only
-> Loading baseline RTMonoDepth from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Warning: Baseline evaluation failed: Baseline model files not found in /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep/models
Error: No models could be evaluated successfully

